﻿using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json.Linq;
using OpenCvSharp;

namespace Shumei.Services
{
    public static class ShuMeiSlide
    {
        /// <summary>
        /// 识别
        /// </summary>
        /// <param name="organization"></param>
        /// <returns></returns>
        public static async Task<string> Recognize(string organization)
        {
            //var proxy = await ProxyUtils.GetProxyIp();
            using HttpClient httpClient = new HttpClient(new HttpClientHandler() {  });
            var time = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            string url = $"https://captcha1.fengkongcloud.cn/ca/v1/register?model=slide&sdkver=1.1.3&callback=sm_{time}&rversion=1.0.1&appId=review-panel&lang=en&data=%7B%7D&organization={organization}&channel=DEFAULT";
            string content;
            try
            {
                var resp = await httpClient.GetAsync(url);
                if (!resp.IsSuccessStatusCode)
                    return string.Empty;
                content = await resp.Content.ReadAsStringAsync();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return string.Empty;
            }

            Match match = Regex.Match(content, @"\((?<json>.*)\)");
            string pureJson = match.Groups["json"].Value;
            JObject json = JObject.Parse(pureJson);
            if (json["riskLevel"]?.ToString() != "PASS")
                return string.Empty;

            var detail = json["detail"];
            if (detail == null)
                return string.Empty;

            var bg = detail["bg"]?.ToString();
            var fg = detail["fg"]?.ToString();
            var rid = detail["rid"]?.ToString();
            if (string.IsNullOrEmpty(bg) || string.IsNullOrEmpty(fg) || string.IsNullOrEmpty(rid))
                return string.Empty;
            var bgBytes = await ProxyUtils.Http.GetByteArrayAsync($"https://castatic.fengkongcloud.cn{bg}");
            var fgBytes = await ProxyUtils.Http.GetByteArrayAsync($"https://castatic.fengkongcloud.cn{fg}");

            var intX = IdentifyGap(fgBytes, bgBytes);
            if (intX == 0)
                return string.Empty;
            var trajectories = GenerateTrajectory(intX);
            var strX = (intX / 300.0).ToString("0.0000");

            var uk = DesEncrypt(strX, "59fcff86");
            var gm = DesEncrypt(trajectories.Key, "0569c403");
            var zg = DesEncrypt(trajectories.Value.ToString(), "65986a6b");
            var callback = $"sm_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}";
            var captchaUuid = string.Empty;

            url =
                $"https://captcha1.fengkongcloud.cn/ca/v2/fverify?aj=Z8JptdSbQHg%3D&tb={uk}&callback={callback}&tm={gm}&uc=b8IY1XIB1iA%3D&rversion=1.0.4&jp=Wq4jwGqOHYM%3D&ly={zg}&rid={rid}&sdkver=1.1.3&wz=ufdT5h7SVes%3D&sy=lN908%2F15DcI%3D&captchaUuid={captchaUuid}&fc=2VKLJM6OJCc%3D&gp=7kP9OL4ZRNU%3D&protocol=184&act.os=web_pc&og=IxbGpVfruz0%3D&dj=7SnISxDhfjI%3D&ostype=web&organization={organization}";
            try
            {
                var resp = await httpClient.GetAsync(url);
                if (!resp.IsSuccessStatusCode)
                    return string.Empty;

                content = await resp.Content.ReadAsStringAsync();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return string.Empty;
            }

            match = Regex.Match(content, @"\((?<json>.*)\)");
            pureJson = match.Groups["json"].Value;
            json = JObject.Parse(pureJson);
            return json["riskLevel"]?.ToString() == "PASS" ? rid : string.Empty;
        }

        private static int IdentifyGap(byte[] fg, byte[] bg)
        {
            try
            {
                using Mat bgImg = Cv2.ImDecode(bg, ImreadModes.Grayscale);
                using Mat tpImg = Cv2.ImDecode(fg, ImreadModes.Grayscale);
                using Mat bgEdge = new Mat();
                using Mat tpEdge = new Mat();
                using Mat bgPic = new Mat();
                using Mat tpPic = new Mat();
                using Mat result = new Mat();
                // 边缘检测
                Cv2.Canny(bgImg, bgEdge, 100, 200);
                Cv2.Canny(tpImg, tpEdge, 100, 200);

                // 转换图片格式
                Cv2.CvtColor(bgEdge, bgPic, ColorConversionCodes.GRAY2BGR);
                Cv2.CvtColor(tpEdge, tpPic, ColorConversionCodes.GRAY2BGR);

                // 模板匹配
                Cv2.MatchTemplate(bgPic, tpPic, result, TemplateMatchModes.CCoeffNormed);
                Cv2.MinMaxLoc(result, out _, out _, out _, out var maxLoc);

                // 计算缺口的 X 坐标
                int hkX = maxLoc.X / 2;

                // 返回缺口的 X 坐标
                return hkX;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return 0;
            }
        }

        private static KeyValuePair<string, int> GenerateTrajectory(int x)
        {
            int headY = Random.Shared.Next(0, 22);
            if (Random.Shared.Next(1, 11) > 7)
            {
                if (Random.Shared.Next(1, 11) <= 5)
                    headY -= 1;
                else
                    headY += 1;
            }

            string trajectoryHead = $"[0,{headY},0]";
            int startXCount = x >= 100 ? 2 : 1;

            StringBuilder trajectoryMiddle = new StringBuilder();
            int currentX = 0, currentY = headY, currentTime;

            // 开头循环
            for (int startI = 1; startI <= startXCount; startI++)
            {
                double firstTime = Random.Shared.Next(30, 51) / 100.0;
                int deltaX = (int)(firstTime * x);
                currentX += deltaX;

                if (Random.Shared.Next(1, 11) >= 5)
                    currentY -= Random.Shared.Next(2, 7);
                else
                    currentY += Random.Shared.Next(2, 7);

                currentTime = startI * 100 + Random.Shared.Next(1, 11);
                trajectoryMiddle.Append($",[ {currentX},{currentY},{currentTime}]");
            }

            // 中间循环
            int totalCount = Random.Shared.Next(2, 6);
            for (int count = 1; count <= totalCount; count++)
            {
                double percentX = Random.Shared.Next(10, 21) / 100.0;
                int deltaX = (int)(percentX * x);
                currentX += deltaX;

                if (currentX > x)
                {
                    currentX -= deltaX;
                    if (Random.Shared.Next(1, 11) >= 5)
                        currentX += 1;
                    else
                        currentX -= 1;
                }

                currentTime = (startXCount + count) * 100 + Random.Shared.Next(1, 11);

                if (currentY <= -5)
                    currentY += 1;
                else if (currentY >= 19)
                    currentY -= 1;
                else
                {
                    if (Random.Shared.Next(1, 11) >= 5)
                        currentY -= 1;
                    else
                        currentY += 1;
                }

                trajectoryMiddle.Append($",[ {currentX},{currentY},{currentTime}]");
            }

            // 尾部循环
            int tailCount = Random.Shared.Next(1, 4);
            for (int tailIndex = 1; tailIndex <= tailCount; tailIndex++)
            {
                if (currentX > x)
                    currentX -= 1;
                else
                    currentX += 1;

                currentTime = (startXCount + totalCount + tailIndex) * 100 + Random.Shared.Next(1, 11);

                if (currentY <= -5)
                    currentY += 1;
                else if (currentY >= 19)
                    currentY -= 1;
                else
                {
                    if (Random.Shared.Next(1, 11) >= 5)
                        currentY -= 1;
                    else
                        currentY += 1;
                }

                trajectoryMiddle.Append($",[ {currentX},{currentY},{currentTime}]");
            }

            // 尾前循环
            int preTailCount = Random.Shared.Next(3, 11);
            StringBuilder trajectoryPreTail = new StringBuilder();
            for (int preTailIndex = 1; preTailIndex <= preTailCount; preTailIndex++)
            {
                if (preTailIndex == 1)
                {
                    if (Random.Shared.Next(1, 11) >= 5)
                        currentX = x + Random.Shared.Next(1, 5);
                    else
                        currentX = x - Random.Shared.Next(1, 5);
                }
                else
                {
                    if (currentX == x)
                        currentX = x;
                    else if (currentX > x)
                        currentX -= 1;
                    else
                        currentX += 1;
                }

                currentTime = (startXCount + totalCount + tailCount + preTailIndex) * 100 + Random.Shared.Next(1, 11);
                trajectoryPreTail.Append($",[ {currentX},{currentY},{currentTime}]");
            }

            currentTime = (startXCount + totalCount + tailCount + preTailCount) * 100 + Random.Shared.Next(1, 11);
            string trajectoryTail = $"[{x},{currentY},{currentTime}]";

            string totalTrajectory = $"[{trajectoryHead}{trajectoryMiddle}{trajectoryPreTail},{trajectoryTail}]";
            return new KeyValuePair<string, int>(totalTrajectory, currentTime);
        }

        private static string DesEncrypt(string plainText, string key)
        {
            try
            {
                byte[] keyBytes = Encoding.UTF8.GetBytes(key);
                byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);

                using var des = DES.Create();
                des.Mode = CipherMode.ECB;
                des.Padding = PaddingMode.Zeros;
                des.Key = keyBytes;

                using ICryptoTransform encryptor = des.CreateEncryptor();
                byte[] resultBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
                return Convert.ToBase64String(resultBytes);
            }
            catch (Exception ex)
            {

                Console.WriteLine(ex);
                return string.Empty;
            }
        }
    }
}
