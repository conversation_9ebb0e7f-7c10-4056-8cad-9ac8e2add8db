﻿using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json.Linq;
using OpenCvSharp;

namespace Shumei.Services
{
    public static class ShuMeiSlide
    {
        /// <summary>
        /// 识别
        /// </summary>
        /// <param name="organization"></param>
        /// <returns></returns>
        public static async Task<string> Recognize(string organization)
        {
            //var proxy = await ProxyUtils.GetProxyIp();
            using HttpClient httpClient = new HttpClient(new HttpClientHandler() {  });
            var time = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            string url = $"https://captcha1.fengkongcloud.cn/ca/v1/register?model=slide&sdkver=1.1.3&callback=sm_{time}&rversion=1.0.1&appId=review-panel&lang=en&data=%7B%7D&organization={organization}&channel=DEFAULT";
            string content;
            try
            {
                var resp = await httpClient.GetAsync(url);
                if (!resp.IsSuccessStatusCode)
                    return string.Empty;
                content = await resp.Content.ReadAsStringAsync();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return string.Empty;
            }

            Match match = Regex.Match(content, @"\((?<json>.*)\)");
            string pureJson = match.Groups["json"].Value;
            JObject json = JObject.Parse(pureJson);
            if (json["riskLevel"]?.ToString() != "PASS")
                return string.Empty;

            var detail = json["detail"];
            if (detail == null)
                return string.Empty;

            var bg = detail["bg"]?.ToString();
            var fg = detail["fg"]?.ToString();
            var rid = detail["rid"]?.ToString();
            if (string.IsNullOrEmpty(bg) || string.IsNullOrEmpty(fg) || string.IsNullOrEmpty(rid))
                return string.Empty;
            var bgBytes = await ProxyUtils.Http.GetByteArrayAsync($"https://castatic.fengkongcloud.cn{bg}");
            var fgBytes = await ProxyUtils.Http.GetByteArrayAsync($"https://castatic.fengkongcloud.cn{fg}");

            var intX = IdentifyGap(fgBytes, bgBytes);
            if (intX == 0)
                return string.Empty;
            var trajectories = GenerateTrajectory(intX);
            var strX = (intX / 300.0).ToString("0.0000");

            var uk = DesEncrypt(strX, "59fcff86");
            var gm = DesEncrypt(trajectories.Key, "0569c403");
            var zg = DesEncrypt(trajectories.Value.ToString(), "65986a6b");
            var callback = $"sm_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}";
            var captchaUuid = string.Empty;

            url =
                $"https://captcha1.fengkongcloud.cn/ca/v2/fverify?aj=Z8JptdSbQHg%3D&tb={uk}&callback={callback}&tm={gm}&uc=b8IY1XIB1iA%3D&rversion=1.0.4&jp=Wq4jwGqOHYM%3D&ly={zg}&rid={rid}&sdkver=1.1.3&wz=ufdT5h7SVes%3D&sy=lN908%2F15DcI%3D&captchaUuid={captchaUuid}&fc=2VKLJM6OJCc%3D&gp=7kP9OL4ZRNU%3D&protocol=184&act.os=web_pc&og=IxbGpVfruz0%3D&dj=7SnISxDhfjI%3D&ostype=web&organization={organization}";
            url =
                $"https://captcha1.fengkongcloud.cn/ca/v2/fverify?organization={organization}&ny=cvSxP%2FXnulg%3D&protocol=185&ostype=web&gg=VFWJlGPI%2F07qjJPkm%2BITYBsceq2COT81&callback=sm_1753785295152&hg=w74PwzuWVMWGa6yDfivyNdKrBY%2BHepPvyXG%2BjyZpC05ub%2FG9L29bYaa8LXR%2FrsJX7gytr6x7vNCAHE05TnmzlfyifsXgErfeNyckwY2OPNjO1nV42qtXJhzyVXEIQ74eESKTCOGKZQ%2F1c%2Bw3hMrL3mMwuaIsWlka1segYwSPcBC5c0AR6OZOPj9wnL%2BUOJd2DpSt0igQD2g7v2%2F%2FTtaGumF%2FZU8Thjxvdxt1u3r6wCaWozI3FDTO%2BYCmHx0RIUY8SOuiR1y%2B5Z73GW5u5ZES4pPa9O%2F2%2BRtmuVYTdYnuBzL4a4lvQcA8WEF9OMQCi%2BCCrNiwHKR%2F%2BAr3x2nn3hjtDgcPVHV2zp5p8g8qOJTKEzByJPCUxGPgjq%2F3tO5k%2BwprRq1AkvAUb1B7kJom7x0K3YkVqlbPgoF%2B0qO5p%2FIOI30aq2ZgLmRSga5KJaOUx1VA20Yjo%2BWIMDw2vrpwlaaG%2FlkAJ4XWmB2T4r0N9Or42JU%3D&rid=202507291819291112a16433d1fd5654&qt=QcZFnVbE0HA%3D&rversion=1.0.4&sdkver=1.1.3&th=uIG9z6kVoi4%3D&bs=ZGIsKZZObiI%3D&sl=ld3hnK7eLbY%3D&act.os=web_pc&captchaUuid={}&fm=BNYA%2B9sHvZU%3D&bq=1szrpYdSRZQ%3D&to=bftazdO%2BdKs%3D&yh=wFVE7H%2BkToU%3D&lf=LCntkg8Oqr0%3D";
            try
            {
                var resp = await httpClient.GetAsync(url);
                if (!resp.IsSuccessStatusCode)
                    return string.Empty;

                content = await resp.Content.ReadAsStringAsync();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return string.Empty;
            }

            match = Regex.Match(content, @"\((?<json>.*)\)");
            pureJson = match.Groups["json"].Value;
            json = JObject.Parse(pureJson);
            return json["riskLevel"]?.ToString() == "PASS" ? rid : string.Empty;
        }

        private static int IdentifyGap(byte[] fg, byte[] bg)
        {
            try
            {
                using Mat bgImg = Cv2.ImDecode(bg, ImreadModes.Grayscale);
                using Mat tpImg = Cv2.ImDecode(fg, ImreadModes.Grayscale);
                using Mat bgEdge = new Mat();
                using Mat tpEdge = new Mat();
                using Mat bgPic = new Mat();
                using Mat tpPic = new Mat();
                using Mat result = new Mat();
                // 边缘检测
                Cv2.Canny(bgImg, bgEdge, 100, 200);
                Cv2.Canny(tpImg, tpEdge, 100, 200);

                // 转换图片格式
                Cv2.CvtColor(bgEdge, bgPic, ColorConversionCodes.GRAY2BGR);
                Cv2.CvtColor(tpEdge, tpPic, ColorConversionCodes.GRAY2BGR);

                // 模板匹配
                Cv2.MatchTemplate(bgPic, tpPic, result, TemplateMatchModes.CCoeffNormed);
                Cv2.MinMaxLoc(result, out _, out _, out _, out var maxLoc);

                // 计算缺口的 X 坐标
                int hkX = maxLoc.X / 2;

                // 返回缺口的 X 坐标
                return hkX;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return 0;
            }
        }

        private static KeyValuePair<string, int> GenerateTrajectory(int targetX)
        {
            var trajectory = new List<int[]>();
            var random = new Random();

            // 初始位置，模拟真人点击时的微小偏移
            int startY = random.Next(8, 18);
            int currentX = 0;
            int currentY = startY;
            int currentTime = 0;

            // 添加起始点
            trajectory.Add(new int[] { currentX, currentY, currentTime });

            // 生成更自然的轨迹
            var phases = GenerateMovementPhases(targetX, random);

            foreach (var phase in phases)
            {
                var points = GeneratePhasePoints(phase, targetX, currentX, currentY, currentTime, random);
                foreach (var point in points)
                {
                    currentX = point[0];
                    currentY = point[1];
                    currentTime = point[2];
                    trajectory.Add(point);
                }
            }

            // 添加最终微调阶段
            var finalAdjustments = GenerateFinalAdjustments(targetX, currentX, currentY, currentTime, random);
            trajectory.AddRange(finalAdjustments);

            // 构建轨迹字符串
            var trajectoryStr = "[" + string.Join(",", trajectory.Select(p => $"[{p[0]},{p[1]},{p[2]}]")) + "]";
            var finalTime = trajectory.Last()[2];

            return new KeyValuePair<string, int>(trajectoryStr, finalTime);
        }

        private static List<MovementPhase> GenerateMovementPhases(int targetX, Random random)
        {
            var phases = new List<MovementPhase>();

            // 阶段1：缓慢启动 (0-20% 距离)
            phases.Add(new MovementPhase
            {
                StartPercent = 0.0,
                EndPercent = 0.15 + random.NextDouble() * 0.1, // 15-25%
                MinSpeed = 0.8,
                MaxSpeed = 2.5,
                YVariation = 3
            });

            // 阶段2：加速阶段 (20-60% 距离)
            phases.Add(new MovementPhase
            {
                StartPercent = phases.Last().EndPercent,
                EndPercent = 0.55 + random.NextDouble() * 0.15, // 55-70%
                MinSpeed = 2.0,
                MaxSpeed = 4.5,
                YVariation = 5
            });

            // 阶段3：减速接近 (60-90% 距离)
            phases.Add(new MovementPhase
            {
                StartPercent = phases.Last().EndPercent,
                EndPercent = 0.85 + random.NextDouble() * 0.1, // 85-95%
                MinSpeed = 1.5,
                MaxSpeed = 3.0,
                YVariation = 4
            });

            // 阶段4：精确定位 (90-100% 距离)
            phases.Add(new MovementPhase
            {
                StartPercent = phases.Last().EndPercent,
                EndPercent = 1.0,
                MinSpeed = 0.5,
                MaxSpeed = 1.8,
                YVariation = 2
            });

            return phases;
        }

        private static List<int[]> GeneratePhasePoints(MovementPhase phase, int targetX, int startX, int startY, int startTime, Random random)
        {
            var points = new List<int[]>();
            var currentX = startX;
            var currentY = startY;
            var currentTime = startTime;

            // 计算这个阶段的目标X位置
            var targetXForPhase = (int)(phase.EndPercent * targetX);
            var phaseDistance = targetXForPhase - startX;
            var pointCount = Math.Max(3, Math.Abs(phaseDistance) / 6 + 2); // 动态计算点数

            for (int i = 0; i < pointCount; i++)
            {
                // 计算时间间隔 (模拟真人的不规律间隔)
                var baseInterval = 15 + random.NextDouble() * 25; // 15-40ms基础间隔
                var speedFactor = phase.MinSpeed + random.NextDouble() * (phase.MaxSpeed - phase.MinSpeed);
                var timeInterval = (int)(baseInterval / speedFactor);

                // 添加随机抖动
                timeInterval += random.Next(-5, 6);
                currentTime += Math.Max(8, timeInterval); // 最小8ms间隔

                // X轴移动 (带有微小的随机性)
                var progress = (double)(i + 1) / pointCount;
                var expectedX = startX + (int)(phaseDistance * progress);
                var xNoise = random.Next(-2, 3); // ±2px的随机偏移
                currentX = Math.Max(0, expectedX + xNoise);

                // Y轴自然抖动
                var yChange = random.Next(-phase.YVariation, phase.YVariation + 1);
                currentY = Math.Max(-8, Math.Min(25, currentY + yChange));

                // 模拟真人的小幅回调行为 (5%概率)
                if (random.NextDouble() < 0.05 && i > 0)
                {
                    currentX = Math.Max(0, currentX - random.Next(1, 4));
                }

                points.Add(new int[] { currentX, currentY, currentTime });
            }

            return points;
        }

        private static List<int[]> GenerateFinalAdjustments(int targetX, int currentX, int currentY, int currentTime, Random random)
        {
            var adjustments = new List<int[]>();
            var adjustmentCount = random.Next(2, 6); // 2-5次微调

            for (int i = 0; i < adjustmentCount; i++)
            {
                currentTime += random.Next(20, 50); // 20-50ms间隔

                // 逐步接近目标位置
                var diff = targetX - currentX;
                if (Math.Abs(diff) > 1)
                {
                    var step = Math.Sign(diff) * Math.Max(1, Math.Abs(diff) / (adjustmentCount - i));
                    currentX += step;
                }
                else if (random.NextDouble() < 0.3) // 30%概率进行±1的微调
                {
                    currentX += random.Next(-1, 2);
                }

                // Y轴微小调整
                currentY += random.Next(-1, 2);
                currentY = Math.Max(-8, Math.Min(25, currentY));

                adjustments.Add(new int[] { currentX, currentY, currentTime });
            }

            // 确保最后一个点正好在目标位置
            currentTime += random.Next(15, 35);
            adjustments.Add(new int[] { targetX, currentY, currentTime });

            return adjustments;
        }

        private class MovementPhase
        {
            public double StartPercent { get; set; }
            public double EndPercent { get; set; }
            public double MinSpeed { get; set; }
            public double MaxSpeed { get; set; }
            public int YVariation { get; set; }
        }

        private static string DesEncrypt(string plainText, string key)
        {
            try
            {
                byte[] keyBytes = Encoding.UTF8.GetBytes(key);
                byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);

                using var des = DES.Create();
                des.Mode = CipherMode.ECB;
                des.Padding = PaddingMode.Zeros;
                des.Key = keyBytes;

                using ICryptoTransform encryptor = des.CreateEncryptor();
                byte[] resultBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
                return Convert.ToBase64String(resultBytes);
            }
            catch (Exception ex)
            {

                Console.WriteLine(ex);
                return string.Empty;
            }
        }
    }
}
